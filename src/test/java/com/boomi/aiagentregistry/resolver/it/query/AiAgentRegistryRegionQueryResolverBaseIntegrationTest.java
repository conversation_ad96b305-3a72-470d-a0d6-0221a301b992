package com.boomi.aiagentregistry.resolver.it.query;

import com.boomi.aiagentregistry.config.BaseMockWebServerTest;
import com.boomi.aiagentregistry.util.CommonFeatureAndPrivilegeUtil;
import com.boomi.aiagentregistry.util.GraphQLFileReaderUtil;
import com.boomi.aiagentregistry.util.GraphQLQueriesEnum;
import com.boomi.aiagentregistry.util.GraphqlTypeEnum;
import com.boomi.aiagentregistry.util.TestUtil;
import com.boomi.services.test.graphql.TestWebFluxGraphQLExecutor;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.IOException;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * Abstract base class for AI Agent Registry Region Query Resolver integration tests.
 * Contains common test logic that can be shared across different test configurations.
 * 
 * This follows the Template Method pattern where concrete test classes define the configuration
 * and this base class provides the common test execution logic.
 */
public abstract class AiAgentRegistryRegionQueryResolverBaseIntegrationTest extends BaseMockWebServerTest {

    @Autowired
    protected TestWebFluxGraphQLExecutor executor;

    /**
     * Executes the region query test with the provided configuration.
     * This method contains the common test logic that validates the AI Agent Regions query response.
     * 
     * @param config The test configuration specifying expected behavior
     * @throws IOException if there's an error reading the GraphQL query or parsing the response
     */
    protected void executeRegionQueryTest(RegionTestConfig config) throws IOException {
        // Read the GraphQL query
        String queryStr = GraphQLFileReaderUtil.readGraphQLQuery(
                GraphqlTypeEnum.QUERY.getGraphqlType(),
                GraphQLQueriesEnum.AI_AGENT_REGIONS.getFileName()
        );
        assertNotNull(queryStr, "GraphQL query string should not be null");

        // Execute the query
        final String response = executor.executeAtomSphereQuery(
                queryStr, 
                TestUtil.COMMON_ACCOUNT_ID,
                CommonFeatureAndPrivilegeUtil.FEATURES, 
                TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES
        );
        assertNotNull(response, "GraphQL response should not be null");

        // Parse the response
        ObjectMapper mapper = new ObjectMapper();
        JsonNode node = mapper.readTree(response);
        JsonNode aiAgentRegions = node.get("data").get("aiAgentRegions");
        assertNotNull(aiAgentRegions, "aiAgentRegions should not be null in response");

        // Validate the response based on configuration
        validateRegionResponse(aiAgentRegions, config);
    }

    /**
     * Validates the region response based on the test configuration.
     * 
     * @param aiAgentRegions The regions JSON node from the GraphQL response
     * @param config The test configuration with expected values
     */
    private void validateRegionResponse(JsonNode aiAgentRegions, RegionTestConfig config) {
        // Validate region count if specified
        if (config.getExpectedRegionCount() != null) {
            assertEquals(config.getExpectedRegionCount().intValue(), aiAgentRegions.size(),
                    "Number of regions should match expected count");
        } else {
            assertTrue(aiAgentRegions.size() > 0, "Should return at least one region");
        }

        // Validate specific regions if configured
        if (config.isValidateSpecificRegions() && config.getExpectedRegionCodes() != null) {
            Set<String> actualRegionCodes = StreamSupport.stream(aiAgentRegions.spliterator(), false)
                    .map(region -> region.path("code").asText())
                    .collect(Collectors.toSet());

            assertTrue(actualRegionCodes.containsAll(config.getExpectedRegionCodes()),
                    "Actual regions should contain all expected regions. Expected: " + 
                    config.getExpectedRegionCodes() + ", Actual: " + actualRegionCodes);
        }

        // Validate first region structure if configured
        if (config.isValidateRegionStructure() && aiAgentRegions.size() > 0) {
            JsonNode firstRegion = aiAgentRegions.get(0);
            assertNotNull(firstRegion.get("code"), "Region should have a code");
            assertNotNull(firstRegion.get("label"), "Region should have a label");
        }

        // Validate specific first region code if configured
        if (config.getExpectedFirstRegionCode() != null && aiAgentRegions.size() > 0) {
            String actualFirstRegionCode = aiAgentRegions.get(0).get("code").asText();
            assertEquals(config.getExpectedFirstRegionCode(), actualFirstRegionCode,
                    "First region code should match expected value");
        }
    }

    /**
     * Configuration class for region query tests.
     * Defines the expected behavior and validation criteria for different test scenarios.
     */
    public static class RegionTestConfig {
        private final Integer expectedRegionCount;
        private final Set<String> expectedRegionCodes;
        private final boolean validateSpecificRegions;
        private final boolean validateRegionStructure;
        private final String expectedFirstRegionCode;

        private RegionTestConfig(Builder builder) {
            this.expectedRegionCount = builder.expectedRegionCount;
            this.expectedRegionCodes = builder.expectedRegionCodes;
            this.validateSpecificRegions = builder.validateSpecificRegions;
            this.validateRegionStructure = builder.validateRegionStructure;
            this.expectedFirstRegionCode = builder.expectedFirstRegionCode;
        }

        public Integer getExpectedRegionCount() {
            return expectedRegionCount;
        }

        public Set<String> getExpectedRegionCodes() {
            return expectedRegionCodes;
        }

        public boolean isValidateSpecificRegions() {
            return validateSpecificRegions;
        }

        public boolean isValidateRegionStructure() {
            return validateRegionStructure;
        }

        public String getExpectedFirstRegionCode() {
            return expectedFirstRegionCode;
        }

        public static Builder builder() {
            return new Builder();
        }

        public static class Builder {
            private Integer expectedRegionCount;
            private Set<String> expectedRegionCodes;
            private boolean validateSpecificRegions = false;
            private boolean validateRegionStructure = true;
            private String expectedFirstRegionCode;

            public Builder expectedRegionCount(Integer count) {
                this.expectedRegionCount = count;
                return this;
            }

            public Builder expectedRegionCodes(Set<String> codes) {
                this.expectedRegionCodes = codes;
                return this;
            }

            public Builder validateSpecificRegions(boolean validate) {
                this.validateSpecificRegions = validate;
                return this;
            }

            public Builder validateRegionStructure(boolean validate) {
                this.validateRegionStructure = validate;
                return this;
            }

            public Builder expectedFirstRegionCode(String code) {
                this.expectedFirstRegionCode = code;
                return this;
            }

            public RegionTestConfig build() {
                return new RegionTestConfig(this);
            }
        }
    }
}
