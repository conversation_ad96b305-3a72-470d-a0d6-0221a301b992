package com.boomi.aiagentregistry.resolver.it.query;

import com.boomi.aiagentregistry.TestApplication;
import com.boomi.aiagentregistry.config.BaseMockWebServerTest;
import com.boomi.aiagentregistry.util.CommonFeatureAndPrivilegeUtil;
import com.boomi.aiagentregistry.util.GraphQLFileReaderUtil;
import com.boomi.aiagentregistry.util.GraphQLQueriesEnum;
import com.boomi.aiagentregistry.util.GraphqlTypeEnum;
import com.boomi.aiagentregistry.util.TestUtil;
import com.boomi.services.test.graphql.TestWebFluxGraphQLExecutor;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import java.io.IOException;
import java.util.HashSet;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

@SpringBootTest(classes = TestApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureMockMvc
@TestPropertySource(properties = {
    "boomi.services.aiagentregistry.use.bedrock.assume.role=false"
})
public class AiAgentRegistryRegionQueryResolverImplAssumeRoleDisabledIntegrationTest extends BaseMockWebServerTest {

    @Autowired
    private TestWebFluxGraphQLExecutor executor;

    @Test
    @DisplayName("Test Successful execution of aiAgentRegion query with assumeRole disabled")
    public void testAiAgentsWithAssumeRoleDisabled() throws IOException {

        String queryStr = GraphQLFileReaderUtil.readGraphQLQuery(GraphqlTypeEnum.QUERY.getGraphqlType(),
                GraphQLQueriesEnum.AI_AGENT_REGIONS.getFileName());

        assertNotNull(queryStr);

        final String response = executor.executeAtomSphereQuery(queryStr, TestUtil.COMMON_ACCOUNT_ID,
                CommonFeatureAndPrivilegeUtil.FEATURES, TestUtil.COMMON_USER_NAME,
                CommonFeatureAndPrivilegeUtil.BASIC_PRIVILEGES);

        assertNotNull(response);

        ObjectMapper mapper = new ObjectMapper();
        JsonNode node = mapper.readTree(response);
        JsonNode aiAgentRegions = node.get("data").get("aiAgentRegions");

        assertNotNull(aiAgentRegions);
        assertEquals(17, aiAgentRegions.size());

        Set<String> expectedRegionCodes = Set.of(
                "us-east-1", "us-east-2", "us-west-2", "us-gov-east-1", "us-gov-west-1",
                "ap-northeast-1", "ap-northeast-2", "ap-south-1", "ap-southeast-1", "ap-southeast-2",
                "ca-central-1", "eu-central-1", "eu-central-2", "eu-west-1", "eu-west-2", "eu-west-3",
                "sa-east-1"
        );

        Set<String> actualRegionCodes = StreamSupport.stream(aiAgentRegions.spliterator(), true)
                .map(region -> region.path("code").asText())
                .collect(Collectors.toSet());

        assertTrue(actualRegionCodes.containsAll(expectedRegionCodes));
    }
}
