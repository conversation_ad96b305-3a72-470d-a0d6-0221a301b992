package com.boomi.aiagentregistry.resolver.it.query;

import java.util.Set;

/**
 * Predefined test configurations for AI Agent Registry Region Query tests.
 * This class contains static configurations for different test scenarios,
 * following the industry standard of centralizing test data and configurations.
 */
public final class RegionTestConfigurations {

    private RegionTestConfigurations() {
        // Utility class - prevent instantiation
    }

    /**
     * Configuration for testing when assume role is enabled (default behavior).
     * When assume role is enabled, the system typically returns regions from OamSinkArnRetrieverService,
     * which in test environments usually returns a limited set of regions.
     */
    public static final AiAgentRegistryRegionQueryResolverBaseIntegrationTest.RegionTestConfig ASSUME_ROLE_ENABLED =
            AiAgentRegistryRegionQueryResolverBaseIntegrationTest.RegionTestConfig.builder()
                    .validateRegionStructure(true)
                    .expectedFirstRegionCode("us-east-1")
                    .build();

    /**
     * Configuration for testing when assume role is disabled.
     * When assume role is disabled, the system returns a comprehensive list of all supported AWS regions.
     * This configuration validates the complete set of 17 expected regions.
     */
    public static final AiAgentRegistryRegionQueryResolverBaseIntegrationTest.RegionTestConfig ASSUME_ROLE_DISABLED =
            AiAgentRegistryRegionQueryResolverBaseIntegrationTest.RegionTestConfig.builder()
                    .expectedRegionCount(17)
                    .expectedRegionCodes(getAllSupportedRegions())
                    .validateSpecificRegions(true)
                    .validateRegionStructure(true)
                    .build();

    /**
     * Returns the complete set of supported AWS regions when assume role is disabled.
     * This represents all the regions that the AI Agent Registry supports.
     * 
     * @return Set of all supported region codes
     */
    private static Set<String> getAllSupportedRegions() {
        return Set.of(
                "us-east-1", "us-east-2", "us-west-2", "us-gov-east-1", "us-gov-west-1",
                "ap-northeast-1", "ap-northeast-2", "ap-south-1", "ap-southeast-1", "ap-southeast-2",
                "ca-central-1", "eu-central-1", "eu-central-2", "eu-west-1", "eu-west-2", "eu-west-3",
                "sa-east-1"
        );
    }
}
